{"name": "@nv2/nv2-pkg-js-shared-components", "version": "2.45.2", "files": ["lib"], "repository": {"type": "git", "url": "****************************:nv2/pkg/js/nv2-pkg-js-shared-components.git"}, "dependencies": {"@craco/craco": "^6.4.5", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@mui/lab": "^5.0.0-alpha.96", "@mui/material": "^5.10.2", "@mui/styles": "^5.10.2", "@mui/x-data-grid": "^6.2.0", "@mui/x-date-pickers": "^5.0.11", "@nv2/nv2-pkg-js-theme": "^2.8.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.17.1", "craco-sass-resources-loader": "^1.1.0", "dayjs": "^1.11.10", "jest-junit": "^14.0.1", "leaflet": "^1.9.4", "lodash": "^4.17.21", "material-table": "^2.0.3", "postcss": "^8.4.16", "postcss-loader": "^7.0.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dotdotdot": "^1.3.1", "react-gtm-module": "^2.0.11", "react-icons": "^4.4.0", "react-leaflet": "^4.2.1", "react-pro-sidebar": "^1.1.0-alpha.1", "react-redux": "^7.2.5", "react-redux-toastr": "^7.6.5", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "redux": "^4.2.0", "redux-logger": "^3.0.6", "sass-loader": "^8.0.2", "tinycolor2": "^1.4.2", "typescript": "^5.0.4"}, "scripts": {"start": "GENERATE_SOURCEMAP=false craco start", "build": "craco build", "lint": "eslint --ext .js --ext .jsx src", "lib": "babel src/components --out-dir lib --extensions \".ts,.tsx,.js,.jsx\" --copy-files", "test": "jest --config ./jest.config.js --collectCoverage", "test:coverage": "CI=true npm test -- --env=jsdom --coverage", "eject": "craco eject", "semantic-release": "semantic-release", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "publishConfig": {"access": "public"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.5", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^9.4.2", "@semantic-release/npm": "^9.0.1", "@storybook/addon-actions": "^7.0.9", "@storybook/addon-essentials": "^7.0.9", "@storybook/addon-links": "^7.0.9", "@storybook/addon-mdx-gfm": "^7.0.9", "@storybook/cli": "^7.0.9", "@storybook/node-logger": "^7.0.9", "@storybook/preset-create-react-app": "^7.0.9", "@storybook/react": "^7.0.9", "@storybook/react-webpack5": "^7.0.9", "@testing-library/react-hooks": "^8.0.1", "@types/jest": "^29.5.1", "babel-jest": "^29.5.0", "eslint": "^8.24.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-storybook": "^0.6.12", "file-system-cache": "1.0.5", "history": "^5.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.0.3", "react-docgen-typescript-plugin": "^1.0.5", "semantic-release": "^19.0.5", "storybook": "^7.0.9", "ts-jest": "^29.1.0"}}